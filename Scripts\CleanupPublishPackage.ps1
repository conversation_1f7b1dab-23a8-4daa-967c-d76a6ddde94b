# 清理发布包脚本 - 移除不必要的文件以减小包大小
param(
    [Parameter(Mandatory=$true)]
    [string]$PackagePath
)

Write-Host "开始清理发布包: $PackagePath" -ForegroundColor Green

if (-not (Test-Path $PackagePath)) {
    Write-Error "路径不存在: $PackagePath"
    exit 1
}

# 要删除的文件模式
$FilesToRemove = @(
    "*.pdb",           # 调试符号文件
    "*.xml",           # XML 文档文件
    "*.deps.json",     # 依赖文件（保留主程序的）
    "mscordaccore*.dll", # 调试相关
    "mscordbi.dll",    # 调试相关
    "createdump.exe"   # 调试工具
)

# 要删除的语言文件夹（保留中文）
$LanguagesToRemove = @(
    "cs", "de", "es", "fr", "it", "ja", "ko", "pl", "pt-BR", "ru", "tr", "zh-Hant"
)

# 要删除的不必要的 ASP.NET Core 组件
$UnnecessaryDlls = @(
    "*SignalR*",
    "*Identity*", 
    "*Authentication*",
    "*Authorization*",
    "*Mvc*",
    "*Razor*",
    "*Components*",
    "*Blazor*"
)

Write-Host "删除调试和文档文件..." -ForegroundColor Yellow
foreach ($pattern in $FilesToRemove) {
    $files = Get-ChildItem -Path $PackagePath -Filter $pattern -Recurse
    foreach ($file in $files) {
        # 保留主程序的 deps.json 文件
        if ($file.Name -like "*.deps.json" -and $file.Name -like "Rebecca*") {
            continue
        }
        Write-Host "删除: $($file.FullName)"
        Remove-Item $file.FullName -Force
    }
}

Write-Host "删除不需要的语言资源..." -ForegroundColor Yellow
foreach ($lang in $LanguagesToRemove) {
    $langPath = Join-Path $PackagePath $lang
    if (Test-Path $langPath) {
        Write-Host "删除语言文件夹: $langPath"
        Remove-Item $langPath -Recurse -Force
    }
}

Write-Host "删除不必要的 ASP.NET Core 组件..." -ForegroundColor Yellow
foreach ($pattern in $UnnecessaryDlls) {
    $files = Get-ChildItem -Path $PackagePath -Filter "$pattern.dll" -Recurse
    foreach ($file in $files) {
        Write-Host "删除: $($file.FullName)"
        Remove-Item $file.FullName -Force
    }
}

# 计算清理后的大小
$totalSize = (Get-ChildItem -Path $PackagePath -Recurse | Measure-Object -Property Length -Sum).Sum
$sizeInMB = [math]::Round($totalSize / 1MB, 2)

Write-Host "清理完成！当前包大小: $sizeInMB MB" -ForegroundColor Green
