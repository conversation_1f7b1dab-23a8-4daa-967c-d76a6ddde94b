﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
    <WebProjectPath>../web</WebProjectPath>
    <WebDistPath>$(WebProjectPath)/dist</WebDistPath>
    <UseWindowsForms>true</UseWindowsForms>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <Platforms>AnyCPU;x64;x86</Platforms>
    <SelfContained>false</SelfContained>
    <PublishSingleFile>false</PublishSingleFile>
  </PropertyGroup>

  <ItemGroup>
    <AdditionalFiles Remove="app.manifest" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="favicon.ico" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.3065.39" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
    <PackageReference Include="StdEx" Version="1.0.12" />
  </ItemGroup>

  <!-- 只在Release模式下嵌入前端文件 -->
  <Target Name="BuildWebRelease" BeforeTargets="BeforeBuild" Condition="'$(Configuration)' == 'Release'">
    <Message Text="Building web project for release..." Importance="high" />
    <Exec Command="npm install" WorkingDirectory="$(WebProjectPath)" />
    <Exec Command="npm run build" WorkingDirectory="$(WebProjectPath)" />
    <ItemGroup>
      <_WebDistFiles Include="$(WebDistPath)/**/*.*" />
      <EmbeddedResource Include="@(_WebDistFiles)">
        <LogicalName>wwwroot\%(RecursiveDir)$([System.String]::Copy('%(Filename)%(Extension)'))</LogicalName>
      </EmbeddedResource>
    </ItemGroup>
  </Target>

  <Target Name="CleanReleaseFolder" AfterTargets="Build" Condition="'$(Configuration)' == 'Release'">
	<PropertyGroup>
      <OutputFolder>$(MSBuildProjectDirectory)\$(OutputPath)</OutputFolder>
      <WebViewFolder>$(OutputPath)Rebecca.exe.WebView2</WebViewFolder>
      <RuntimeArm64Folder>$(OutputPath)runtimes\win-arm64</RuntimeArm64Folder>
	</PropertyGroup>
    <Message Text="MSBuildProjectDirectory=$(MSBuildProjectDirectory)" Importance="high" />
    <Message Text="OutputPath=$(OutputPath)" Importance="high" />
    <Exec Command="PowerShell -Command &quot;Remove-Item -Path '$(OutputFolder)*.pdb'&quot;" />
    <Exec Command="PowerShell -Command &quot;Remove-Item -Path '$(OutputFolder)*.xml'&quot;" />
    <Exec Command="PowerShell -Command &quot;Remove-Item -Path '$(OutputFolder)Microsoft.Web.WebView2.WinForms.dll'&quot;" />
    <Exec Command="PowerShell -Command &quot;if (Test-Path '$(WebViewFolder)') { Remove-Item -Recurse -Path '$(WebViewFolder)' }&quot;" />
    <Exec Command="PowerShell -Command &quot;if (Test-Path '$(RuntimeArm64Folder)') { Remove-Item -Recurse -Path '$(RuntimeArm64Folder)' }&quot;" />
  </Target>
</Project>
