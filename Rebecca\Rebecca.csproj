<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
    <WebProjectPath>../web</WebProjectPath>
    <WebDistPath>$(WebProjectPath)/dist</WebDistPath>
    <UseWindowsForms>true</UseWindowsForms>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <Platforms>AnyCPU;x64;x86</Platforms>
    <SelfContained>false</SelfContained>
    <PublishSingleFile>false</PublishSingleFile>
    <!-- 优化发布包大小 -->
    <PublishTrimmed>false</PublishTrimmed>
    <SatelliteResourceLanguages>zh-CN</SatelliteResourceLanguages>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
  </PropertyGroup>

  <ItemGroup>
    <AdditionalFiles Remove="app.manifest" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="favicon.ico" />
  </ItemGroup>

  <ItemGroup>
    <!-- 只引用实际需要的 ASP.NET Core 包 -->
    <PackageReference Include="Microsoft.AspNetCore.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.StaticFiles" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.3065.39" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
    <PackageReference Include="StdEx" Version="1.0.12" />
  </ItemGroup>

  <!-- 只在Release模式下嵌入前端文件 -->
  <Target Name="BuildWebRelease" BeforeTargets="BeforeBuild" Condition="'$(Configuration)' == 'Release'">
    <Message Text="Building web project for release..." Importance="high" />
    <Exec Command="npm install" WorkingDirectory="$(WebProjectPath)" />
    <Exec Command="npm run build" WorkingDirectory="$(WebProjectPath)" />
    <ItemGroup>
      <_WebDistFiles Include="$(WebDistPath)/**/*.*" />
      <EmbeddedResource Include="@(_WebDistFiles)">
        <LogicalName>wwwroot\%(RecursiveDir)$([System.String]::Copy('%(Filename)%(Extension)'))</LogicalName>
      </EmbeddedResource>
    </ItemGroup>
  </Target>

  <Target Name="CleanReleaseFolder" AfterTargets="Build" Condition="'$(Configuration)' == 'Release'">
	<PropertyGroup>
      <OutputFolder>$(MSBuildProjectDirectory)\$(OutputPath)</OutputFolder>
      <WebViewFolder>$(OutputPath)Rebecca.exe.WebView2</WebViewFolder>
      <RuntimeArm64Folder>$(OutputPath)runtimes\win-arm64</RuntimeArm64Folder>
	</PropertyGroup>
    <Message Text="MSBuildProjectDirectory=$(MSBuildProjectDirectory)" Importance="high" />
    <Message Text="OutputPath=$(OutputPath)" Importance="high" />
    <!-- 删除调试和文档文件 -->
    <Exec Command="PowerShell -Command &quot;Remove-Item -Path '$(OutputFolder)*.pdb' -ErrorAction SilentlyContinue&quot;" />
    <Exec Command="PowerShell -Command &quot;Remove-Item -Path '$(OutputFolder)*.xml' -ErrorAction SilentlyContinue&quot;" />
    <!-- 删除不需要的 WebView2 文件 -->
    <Exec Command="PowerShell -Command &quot;Remove-Item -Path '$(OutputFolder)Microsoft.Web.WebView2.WinForms.dll' -ErrorAction SilentlyContinue&quot;" />
    <Exec Command="PowerShell -Command &quot;if (Test-Path '$(WebViewFolder)') { Remove-Item -Recurse -Path '$(WebViewFolder)' }&quot;" />
    <!-- 删除不需要的运行时 -->
    <Exec Command="PowerShell -Command &quot;if (Test-Path '$(RuntimeArm64Folder)') { Remove-Item -Recurse -Path '$(RuntimeArm64Folder)' }&quot;" />
    <!-- 删除不需要的语言资源 -->
    <Exec Command="PowerShell -Command &quot;@('cs','de','es','fr','it','ja','ko','pl','pt-BR','ru','tr','zh-Hant') | ForEach-Object { $path = '$(OutputFolder)' + $_; if (Test-Path $path) { Remove-Item -Recurse -Path $path } }&quot;" />
  </Target>
</Project>
