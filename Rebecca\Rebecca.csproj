<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
    <WebProjectPath>../web</WebProjectPath>
    <WebDistPath>$(WebProjectPath)/dist</WebDistPath>
    <UseWindowsForms>true</UseWindowsForms>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <Platforms>AnyCPU;x64;x86</Platforms>
    <SelfContained>false</SelfContained>
    <PublishSingleFile>false</PublishSingleFile>
    <!-- 优化发布包大小 -->
    <PublishTrimmed>false</PublishTrimmed>
    <SatelliteResourceLanguages>zh-CN</SatelliteResourceLanguages>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
  </PropertyGroup>

  <ItemGroup>
    <AdditionalFiles Remove="app.manifest" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="favicon.ico" />
  </ItemGroup>

  <ItemGroup>
    <!-- 使用最小的 ASP.NET Core 框架引用 -->
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.3065.39" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
    <PackageReference Include="StdEx" Version="1.0.12" />
  </ItemGroup>

  <!-- 只在Release模式下嵌入前端文件 -->
  <Target Name="BuildWebRelease" BeforeTargets="BeforeBuild" Condition="'$(Configuration)' == 'Release'">
    <Message Text="Building web project for release..." Importance="high" />
    <Exec Command="npm install" WorkingDirectory="$(WebProjectPath)" />
    <Exec Command="npm run build" WorkingDirectory="$(WebProjectPath)" />
    <ItemGroup>
      <_WebDistFiles Include="$(WebDistPath)/**/*.*" />
      <EmbeddedResource Include="@(_WebDistFiles)">
        <LogicalName>wwwroot\%(RecursiveDir)$([System.String]::Copy('%(Filename)%(Extension)'))</LogicalName>
      </EmbeddedResource>
    </ItemGroup>
  </Target>

  <Target Name="CleanReleaseFolder" AfterTargets="Build" Condition="'$(Configuration)' == 'Release'">
	<PropertyGroup>
      <OutputFolder>$(MSBuildProjectDirectory)\$(OutputPath)</OutputFolder>
      <WebViewFolder>$(OutputPath)Rebecca.exe.WebView2</WebViewFolder>
      <RuntimeArm64Folder>$(OutputPath)runtimes\win-arm64</RuntimeArm64Folder>
	</PropertyGroup>
    <Message Text="开始清理发布文件夹..." Importance="high" />
    <Message Text="OutputPath=$(OutputPath)" Importance="high" />

    <!-- 删除调试和文档文件 -->
    <Exec Command="PowerShell -Command &quot;Get-ChildItem -Path '$(OutputFolder)' -Filter '*.pdb' | Remove-Item -Force -ErrorAction SilentlyContinue&quot;" />
    <Exec Command="PowerShell -Command &quot;Get-ChildItem -Path '$(OutputFolder)' -Filter '*.xml' | Remove-Item -Force -ErrorAction SilentlyContinue&quot;" />

    <!-- 删除不需要的 WebView2 文件 -->
    <Exec Command="PowerShell -Command &quot;Remove-Item -Path '$(OutputFolder)Microsoft.Web.WebView2.WinForms.dll' -Force -ErrorAction SilentlyContinue&quot;" />
    <Exec Command="PowerShell -Command &quot;if (Test-Path '$(WebViewFolder)') { Remove-Item -Recurse -Path '$(WebViewFolder)' -Force }&quot;" />

    <!-- 删除不需要的运行时 -->
    <Exec Command="PowerShell -Command &quot;if (Test-Path '$(RuntimeArm64Folder)') { Remove-Item -Recurse -Path '$(RuntimeArm64Folder)' -Force }&quot;" />

    <!-- 删除不需要的语言资源文件夹 -->
    <Exec Command="PowerShell -Command &quot;@('cs','de','es','fr','it','ja','ko','pl','pt-BR','ru','tr','zh-Hant') | ForEach-Object { $path = Join-Path '$(OutputFolder)' $_; if (Test-Path $path) { Write-Host 'Removing language folder:' $path; Remove-Item -Recurse -Path $path -Force } }&quot;" />

    <!-- 删除不必要的 ASP.NET Core 组件 -->
    <Exec Command="PowerShell -Command &quot;@('*SignalR*','*Identity*','*Authentication*','*Authorization*','*Mvc*','*Razor*','*Components*','*Blazor*') | ForEach-Object { Get-ChildItem -Path '$(OutputFolder)' -Filter ($_ + '.dll') | ForEach-Object { Write-Host 'Removing unnecessary DLL:' $_.Name; Remove-Item $_.FullName -Force -ErrorAction SilentlyContinue } }&quot;" />

    <Message Text="清理完成" Importance="high" />
  </Target>
</Project>
