﻿################################################################################
# 此 .gitignore 文件已由 Microsoft(R) Visual Studio 自动创建。
################################################################################

## Visual Studio temporary files
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates

## Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

## Visual Studio cache files
# files ending in .cache can be ignored
*.[Cc]ache
# but keep track of directories ending in .cache
!?*.[Cc]ache/

## .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

## Visual Studio IntelliCode
.vs/

## ReSharper
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user

## Visual Studio code coverage results
*.coverage
*.coveragexml

## NuGet Packages
*.nupkg
# NuGet Symbol Packages
*.snupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
/Rebecca/web/.vscode
/Rebecca.Package/AppPackages
