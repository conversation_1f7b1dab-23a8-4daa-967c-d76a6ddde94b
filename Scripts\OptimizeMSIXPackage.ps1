# MSIX 包优化脚本 - 在打包后进一步减小包大小
param(
    [Parameter(Mandatory=$false)]
    [string]$PackageBasePath = "Rebecca.Package\AppPackages"
)

Write-Host "开始优化 MSIX 包..." -ForegroundColor Green

# 查找最新的包文件夹
$latestPackage = Get-ChildItem -Path $PackageBasePath -Directory | 
    Where-Object { $_.Name -like "Rebecca.Package_*" } | 
    Sort-Object LastWriteTime -Descending | 
    Select-Object -First 1

if (-not $latestPackage) {
    Write-Error "未找到 MSIX 包文件夹"
    exit 1
}

Write-Host "找到包: $($latestPackage.FullName)" -ForegroundColor Yellow

# 查找 Rebecca 可执行文件夹
$rebeccaFolders = Get-ChildItem -Path $latestPackage.FullName -Recurse -Directory | 
    Where-Object { $_.Name -eq "Rebecca" }

foreach ($rebeccaFolder in $rebeccaFolders) {
    Write-Host "优化文件夹: $($rebeccaFolder.FullName)" -ForegroundColor Cyan
    
    # 删除不必要的 ASP.NET Core 组件
    $unnecessaryPatterns = @(
        "*SignalR*", "*Identity*", "*Authentication*", "*Authorization*",
        "*Mvc*", "*Razor*", "*Components*", "*Blazor*", "*RateLimiting*",
        "*OutputCaching*", "*RequestDecompression*", "*ResponseCaching*",
        "*ResponseCompression*", "*Rewrite*", "*Session*", "*CookiePolicy*",
        "*Cors*", "*HealthChecks*", "*HttpLogging*", "*HttpOverrides*",
        "*HttpsPolicy*", "*Localization*", "*Metadata*"
    )
    
    foreach ($pattern in $unnecessaryPatterns) {
        $files = Get-ChildItem -Path $rebeccaFolder.FullName -Filter "$pattern.dll" -ErrorAction SilentlyContinue
        foreach ($file in $files) {
            Write-Host "  删除: $($file.Name)" -ForegroundColor Red
            Remove-Item $file.FullName -Force -ErrorAction SilentlyContinue
        }
    }
    
    # 删除调试文件
    $debugFiles = Get-ChildItem -Path $rebeccaFolder.FullName -Filter "*.pdb" -ErrorAction SilentlyContinue
    foreach ($file in $debugFiles) {
        Write-Host "  删除调试文件: $($file.Name)" -ForegroundColor Red
        Remove-Item $file.FullName -Force -ErrorAction SilentlyContinue
    }
    
    # 删除文档文件
    $xmlFiles = Get-ChildItem -Path $rebeccaFolder.FullName -Filter "*.xml" -ErrorAction SilentlyContinue
    foreach ($file in $xmlFiles) {
        # 保留 WebView2 的 XML 文件
        if ($file.Name -notlike "*WebView2*") {
            Write-Host "  删除文档文件: $($file.Name)" -ForegroundColor Red
            Remove-Item $file.FullName -Force -ErrorAction SilentlyContinue
        }
    }
    
    # 删除不需要的语言资源
    $languagesToRemove = @("cs", "de", "es", "fr", "it", "ja", "ko", "pl", "pt-BR", "ru", "tr", "zh-Hant")
    foreach ($lang in $languagesToRemove) {
        $langPath = Join-Path $rebeccaFolder.FullName $lang
        if (Test-Path $langPath) {
            Write-Host "  删除语言文件夹: $lang" -ForegroundColor Red
            Remove-Item $langPath -Recurse -Force -ErrorAction SilentlyContinue
        }
    }
    
    # 计算优化后的大小
    $totalSize = (Get-ChildItem -Path $rebeccaFolder.FullName -Recurse -File | Measure-Object -Property Length -Sum).Sum
    $sizeInMB = [math]::Round($totalSize / 1MB, 2)
    Write-Host "  优化后大小: $sizeInMB MB" -ForegroundColor Green
}

Write-Host "MSIX 包优化完成！" -ForegroundColor Green
