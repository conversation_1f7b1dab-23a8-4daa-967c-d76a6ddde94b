{"name": "rebecca-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"axios": "^1.8.1", "element-plus": "^2.9.4", "pinia": "^3.0.1", "sass-embedded": "^1.85.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/node": "^22.13.8", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-typescript": "^14.4.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.20.1", "eslint-plugin-vue": "^9.32.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "typescript": "~5.7.3", "vite": "^6.1.0", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.2"}}